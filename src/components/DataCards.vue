<template>
  <section class="data-section">
    <div class="container-wide">
      <div class="section-header">
        <h2 class="section-title">数据说话，实力见证</h2>
        <p class="section-subtitle">
          已为数千家中小电商企业提供专业的分账系统服务，累计处理资金超25亿元
        </p>
      </div>
      
      <div class="data-grid">
        <div class="data-card" v-for="(item, index) in dataItems" :key="index">
          <div class="card-icon">
            <el-icon :size="40">
              <component :is="item.icon" />
            </el-icon>
          </div>
          <div class="card-content">
            <div class="card-number" ref="numberRefs">{{ item.displayValue }}</div>
            <div class="card-label">{{ item.label }}</div>
            <div class="card-desc">{{ item.description }}</div>
          </div>
          <div class="card-trend" v-if="item.trend">
            <el-icon class="trend-icon" :class="item.trend.type">
              <component :is="item.trend.icon" />
            </el-icon>
            <span class="trend-text">{{ item.trend.text }}</span>
          </div>
        </div>
      </div>

    </div>
  </section>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { 
  TrendCharts, 
  Money, 
  Timer, 
  UserFilled,
  ArrowUp,
  ArrowDown
} from '@element-plus/icons-vue'

const numberRefs = ref([])

const dataItems = ref([
  {
    icon: UserFilled,
    value: 3200,
    displayValue: '3,200+',
    label: '服务企业',
    description: '覆盖电商、跨境、新零售等行业',
    trend: {
      type: 'up',
      icon: ArrowUp,
      text: '月增长 12%'
    }
  },
  {
    icon: Money,
    value: 25,
    displayValue: '25亿',
    label: '累计处理资金',
    description: '安全稳定的资金处理能力',
    trend: {
      type: 'up',
      icon: ArrowUp,
      text: '年增长 85%'
    }
  },
  {
    icon: Timer,
    value: 0,
    displayValue: 'T+0',
    label: '资金到账时效',
    description: '7×24小时实时到账服务',
    trend: null
  },
  {
    icon: TrendCharts,
    value: 99.9,
    displayValue: '99.9%',
    label: '系统稳定性',
    description: '银行级系统架构保障',
    trend: {
      type: 'stable',
      icon: ArrowUp,
      text: '持续稳定'
    }
  }
])



// 数字动画效果
const animateNumbers = () => {
  dataItems.value.forEach((item, index) => {
    if (typeof item.value === 'number' && item.value > 0) {
      let current = 0
      const target = item.value
      const increment = target / 100
      const timer = setInterval(() => {
        current += increment
        if (current >= target) {
          current = target
          clearInterval(timer)
        }
        
        if (item.label.includes('资金')) {
          item.displayValue = `${current.toFixed(0)}亿`
        } else if (item.label.includes('企业')) {
          item.displayValue = `${Math.floor(current).toLocaleString()}+`
        } else if (item.label.includes('稳定性')) {
          item.displayValue = `${current.toFixed(1)}%`
        }
      }, 20)
    }
  })
}

onMounted(() => {
  // 使用 Intersection Observer 来触发动画
  const observer = new IntersectionObserver((entries) => {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        animateNumbers()
        observer.disconnect()
      }
    })
  })
  
  const section = document.querySelector('.data-section')
  if (section) {
    observer.observe(section)
  }
})
</script>

<style scoped>
.data-section {
  padding: 100px 0;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  position: relative;
}

.data-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 10% 20%, rgba(59, 130, 246, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 90% 80%, rgba(16, 185, 129, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

.section-header {
  text-align: center;
  margin-bottom: 4rem;
}

.data-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 2.5rem;
  margin-bottom: 5rem;
}

@media (max-width: 1200px) {
  .data-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .data-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}

.data-card {
  background: white;
  padding: 2.5rem;
  border-radius: 20px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  text-align: center;
  position: relative;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.data-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.card-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  border-radius: 20px;
  color: white;
  margin-bottom: 1.5rem;
}

.card-number {
  font-size: 3rem;
  font-weight: 800;
  color: var(--primary-color);
  margin-bottom: 0.5rem;
  line-height: 1;
}

.card-label {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 0.8rem;
}

.card-desc {
  color: var(--text-light);
  line-height: 1.5;
  margin-bottom: 1rem;
}

.card-trend {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(16, 185, 129, 0.1);
  border-radius: 20px;
  margin-top: 1rem;
}

.trend-icon {
  font-size: 1rem;
}

.trend-icon.up {
  color: #10b981;
}

.trend-icon.down {
  color: #ef4444;
}

.trend-icon.stable {
  color: #6b7280;
}

.trend-text {
  font-size: 0.9rem;
  font-weight: 500;
  color: #10b981;
}



/* 响应式设计 */
@media (max-width: 768px) {
  .data-section {
    padding: 80px 0;
  }

  .data-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .data-card {
    padding: 2rem;
  }

  .card-number {
    font-size: 2.5rem;
  }
}
</style>
