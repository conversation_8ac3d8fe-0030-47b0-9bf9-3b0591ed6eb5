<template>
  <section class="partner-banks-section">
    <div class="container-wide">
      <div class="section-header">
        <h2 class="section-title">合作银行</h2>
        <p class="section-subtitle">
          与多家知名银行深度合作，为您提供安全可靠的金融服务
        </p>
      </div>
      
      <div class="carousel-container">
        <el-carousel
          :interval="5000"
          :autoplay="true"
          indicator-position="outside"
          arrow="always"
          height="220px"
          :loop="true"
          class="bank-carousel"
          trigger="click"
        >
          <el-carousel-item v-for="(group, index) in bankGroups" :key="index">
            <div class="banks-grid">
              <div 
                class="bank-item" 
                v-for="bank in group" 
                :key="bank.name"
                @click="handleBankClick(bank)"
              >
                <div class="bank-logo">
                  <img :src="bank.logo" :alt="bank.name" />
                </div>
                <div class="bank-info">
                  <h3 class="bank-name">{{ bank.name }}</h3>
                  <p class="bank-desc">{{ bank.description }}</p>
                </div>
              </div>
            </div>
          </el-carousel-item>
        </el-carousel>
      </div>
      
      <!-- 银行数量统计 -->
      <div class="bank-stats">
        <div class="stat-item">
          <div class="stat-number">{{ totalBanks }}+</div>
          <div class="stat-label">合作银行</div>
        </div>
        <div class="stat-divider"></div>
        <div class="stat-item">
          <div class="stat-number">100%</div>
          <div class="stat-label">资金安全保障</div>
        </div>
        <div class="stat-divider"></div>
        <div class="stat-item">
          <div class="stat-number">7×24</div>
          <div class="stat-label">服务时间</div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'

// 银行数据
const banks = ref([
  {
    name: '广州农商银行',
    description: '华南地区领先的农商银行',
    logo: 'https://images.unsplash.com/photo-*************-c627a92ad1ab?w=200&h=200&fit=crop&crop=center'
  },
  {
    name: '平安银行',
    description: '中国领先的股份制商业银行',
    logo: 'https://images.unsplash.com/photo-**********-b33ff0c44a43?w=200&h=200&fit=crop&crop=center'
  },
  {
    name: '兴业银行',
    description: '绿色金融领军银行',
    logo: 'https://images.unsplash.com/photo-**********-8d04cb21cd6c?w=200&h=200&fit=crop&crop=center'
  },
  {
    name: '中国建设银行',
    description: '国有大型商业银行',
    logo: 'https://images.unsplash.com/photo-*************-f4d9a9f9297f?w=200&h=200&fit=crop&crop=center'
  },
  {
    name: '招商银行',
    description: '零售银行领先品牌',
    logo: 'https://images.unsplash.com/photo-**********-824ae1b704d3?w=200&h=200&fit=crop&crop=center'
  },
  {
    name: '中国邮政储蓄银行',
    description: '覆盖城乡的大型银行',
    logo: 'https://images.unsplash.com/photo-**********-bebda4e38f71?w=200&h=200&fit=crop&crop=center'
  },
  {
    name: '中国工商银行',
    description: '全球最大银行之一',
    logo: 'https://images.unsplash.com/photo-**********-4b87b5e36e44?w=200&h=200&fit=crop&crop=center'
  }
])

// 将银行分组，每组显示4个
const bankGroups = computed(() => {
  const groups = []
  const banksPerGroup = 4

  for (let i = 0; i < banks.value.length; i += banksPerGroup) {
    const group = banks.value.slice(i, i + banksPerGroup)
    // 如果最后一组少于4个，用前面的银行补齐以保持视觉平衡
    while (group.length < banksPerGroup && groups.length > 0) {
      const fillIndex = (group.length - 1) % banks.value.length
      group.push(banks.value[fillIndex])
    }
    groups.push(group)
  }

  return groups
})

// 银行总数
const totalBanks = computed(() => banks.value.length)

// 点击银行处理
const handleBankClick = (bank) => {
  console.log('点击银行:', bank.name)
  // 这里可以添加点击银行的处理逻辑，比如显示银行详情或跳转到合作页面
  // 可以使用 ElMessage 显示提示
  ElMessage({
    message: `了解更多关于${bank.name}的合作详情`,
    type: 'info',
    duration: 2000
  })
}
</script>

<style scoped>
.partner-banks-section {
  padding: 100px 0;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  position: relative;
}

.partner-banks-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 30%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 80% 70%, rgba(16, 185, 129, 0.03) 0%, transparent 50%);
  pointer-events: none;
}

.section-header {
  text-align: center;
  margin-bottom: 4rem;
  position: relative;
  z-index: 1;
}

.carousel-container {
  margin-bottom: 4rem;
  position: relative;
  z-index: 1;
}

.bank-carousel {
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
}

.banks-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 2rem;
  padding: 2rem;
  height: 100%;
  align-items: center;
  background: white;
}

.bank-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: #ffffff;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
  cursor: pointer;
  height: 160px;
  justify-content: center;
}

.bank-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
  border-color: var(--primary-color);
}

.bank-logo {
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8fafc;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.bank-logo img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}

.bank-info {
  text-align: center;
}

.bank-name {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: 0.25rem;
  line-height: 1.2;
}

.bank-desc {
  font-size: 0.75rem;
  color: var(--text-light);
  line-height: 1.2;
}

.bank-stats {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 2rem;
  padding: 2rem;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  max-width: 600px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 0.9rem;
  color: var(--text-light);
  font-weight: 500;
}

.stat-divider {
  width: 1px;
  height: 40px;
  background: linear-gradient(to bottom, transparent, #e2e8f0, transparent);
}

/* Element Plus Carousel 样式覆盖 */
:deep(.el-carousel__indicator) {
  background-color: rgba(30, 58, 138, 0.3);
}

:deep(.el-carousel__indicator.is-active) {
  background-color: var(--primary-color);
}

:deep(.el-carousel__arrow) {
  background-color: white;
  color: var(--primary-color);
  border: 1px solid #e2e8f0;
}

:deep(.el-carousel__arrow:hover) {
  background-color: var(--primary-color);
  color: white;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .banks-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
  }
}

@media (max-width: 768px) {
  .partner-banks-section {
    padding: 80px 0;
  }
  
  .banks-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    padding: 1.5rem;
  }
  
  .bank-item {
    padding: 1rem;
    height: 120px;
  }
  
  .bank-logo {
    width: 50px;
    height: 50px;
  }
  
  .bank-name {
    font-size: 0.8rem;
  }
  
  .bank-desc {
    font-size: 0.7rem;
  }
  
  .bank-stats {
    gap: 1rem;
    padding: 1.5rem;
  }
  
  .stat-number {
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .bank-carousel {
    height: 180px;
  }

  .banks-grid {
    grid-template-columns: 1fr;
    gap: 0.8rem;
    padding: 1rem;
  }

  .bank-item {
    height: 100px;
    flex-direction: row;
    text-align: left;
    gap: 0.8rem;
  }

  .bank-info {
    text-align: left;
    flex: 1;
  }

  .bank-logo {
    width: 45px;
    height: 45px;
    flex-shrink: 0;
  }

  .bank-name {
    font-size: 0.85rem;
    margin-bottom: 0.2rem;
  }

  .bank-desc {
    font-size: 0.7rem;
  }
}
</style>
